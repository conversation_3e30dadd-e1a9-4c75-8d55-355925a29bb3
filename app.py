from datetime import datetime, time as dtime
import configparser
import webbrowser
from rauth import OAuth1Service
from db_utils import insert_stock_data, act, log_action, get_connection, close_connection, get_inventory_summary, get_profit_summary, get_full_inventory_summary, get_full_profit_summary
import time
from datetime import datetime
import random
import dicttoxml
import xml.etree.ElementTree as ET
from common import log_to_action_log
import requests
import functools
import sys
import json
import os
import argparse
from models import StockData

def reload_config():
    global config
    config.read('config.ini')

# loading configuration file
config = configparser.ConfigParser()
reload_config()

def get_setting(section, key, default, reload_from_ini = False):
    global config
    if reload_from_ini:
        reload_config()
    if not config.has_section(section):
        return default
    if key not in config[section]:
        return default
    return config[section].get(key, default)

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description='E*TRADE API Trading Application',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                           # Run interactively (traditional mode)
  %(prog)s --mode sandbox            # Run in sandbox mode
  %(prog)s --mode prod               # Run in production mode
  %(prog)s --mode none               # Run without E*TRADE API
  %(prog)s --oauth                   # Get OAuth authorization URL and exit
  %(prog)s --oauth --code ABC123     # Complete OAuth with verification code
  %(prog)s --oauth --code ABC123 --debug_preserve_tokens  # Don't delete token file
        """)

    parser.add_argument('--mode', choices=['prod', 'sandbox', 'none'],
                       help='E*TRADE environment mode (overrides config file)')
    parser.add_argument('--oauth', action='store_true',
                       help='Use OAuth workflow (get URL or authenticate with code)')
    parser.add_argument('--code',
                       help='OAuth verification code from E*TRADE')
    parser.add_argument('--debug_preserve_tokens', action='store_true',
                       help='Do not delete OAuth token file after authentication')

    return parser.parse_args()

def determine_environment(args):
    """Determine which environment to use based on args and config"""
    use_etrade_api = True

    if args.mode:
        # Command line argument overrides config
        if args.mode == "sandbox":
            use_sandbox = True
        elif args.mode == "prod":
            use_sandbox = False
        elif args.mode == "none":
            use_sandbox = True
            use_etrade_api = False
    else:
        # Use config file setting
        use_sandbox_setting = config["DEFAULT"].get("USE_SANDBOX", "True").strip().lower()
        if use_sandbox_setting not in ("none", "true", "false"):
            while True:
                resp = input("Choose E*TRADE environment - 0) NONE 1) SANDBOX 2) PROD: ").strip().lower()
                if resp == "0":
                    use_sandbox_setting = "none"
                    break
                elif resp == "1":
                    use_sandbox = True
                    break
                elif resp == "2":
                    use_sandbox = False
                    break
                else:
                    print("Please enter '0', '1' or '2'.")
        elif use_sandbox_setting == "true":
            use_sandbox = True
        elif use_sandbox_setting == "false":
            use_sandbox = False

        if use_sandbox_setting == "none":
            use_sandbox = True
            use_etrade_api = False

    return use_sandbox, use_etrade_api

# Global variables - will be set in main
use_sandbox = None
use_etrade_api = None
base_url = None
consumer_key = None
consumer_secret = None

# Update the settings section to handle the new options
log_none_action_to_db = True
log_none_action_to_file = True

if config.has_section('SETTINGS'):
    log_none_action_setting = config['SETTINGS'].get('LOG_NONE_ACTION', 'true').lower()
    
    # Parse the LOG_NONE_ACTION setting
    if log_none_action_setting in ('false', 'none'):
        log_none_action_to_db = False
        log_none_action_to_file = False
    elif log_none_action_setting == 'db':
        log_none_action_to_db = True
        log_none_action_to_file = False
    elif log_none_action_setting == 'file':
        log_none_action_to_db = False
        log_none_action_to_file = True
    else:  # 'true', 'both', or any other value defaults to logging everywhere
        log_none_action_to_db = True
        log_none_action_to_file = True
    
    # Other settings remain the same
    sb_time_to_sleep_before_checking_order = config['SETTINGS'].getint('SB_TIME_TO_SLEEP_BEFORE_CHECKING_ORDER', fallback=5)
    sb_time_to_sleep_between_trades = config['SETTINGS'].getint('SB_TIME_TO_SLEEP_BETWEEN_TRADES', fallback=6)
    time_to_sleep_before_checking_order = config['SETTINGS'].getint('TIME_TO_SLEEP_BEFORE_CHECKING_ORDER', fallback=15)
    time_to_sleep_between_trades = config['SETTINGS'].getint('TIME_TO_SLEEP_BETWEEN_TRADES', fallback=15)

    # Order type setting
    order_type = config['SETTINGS'].get('ORDER_TYPE', 'MARKET').upper()
    if order_type not in ['MARKET', 'LIMIT']:
        order_type = 'MARKET'  # Default to MARKET if invalid value

def etrade_api_call(func):
    """
    Decorator to add error handling and retry logic to E*TRADE API calls.
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        if not use_etrade_api:
            # If E*TRADE API is disabled, call the function with sandbox behavior
            return func(*args, **kwargs)
        
        # Special case for place_order - no retries to prevent duplicate orders
        if func.__name__ == 'place_order':
            try:
                return func(*args, **kwargs)
            except Exception as e:
                log_to_action_log(f"{func.__name__}: Error occurred: {str(e)}")
                return None
        
        # For all other functions, use retry logic with exponential backoff
        max_retries = 3
        retry_delay = 2  # Start with 2 seconds
        
        for attempt in range(max_retries):
            try:
                # Call the original function
                return func(*args, **kwargs)
                
            except requests.exceptions.Timeout:
                log_to_action_log(f"{func.__name__}: Timeout error on attempt {attempt+1}/{max_retries}")
            except requests.exceptions.ConnectionError:
                log_to_action_log(f"{func.__name__}: Connection error on attempt {attempt+1}/{max_retries}")
            except requests.exceptions.HTTPError as e:
                status_code = e.response.status_code if hasattr(e, 'response') else 'unknown'
                log_to_action_log(f"{func.__name__}: HTTP error {status_code} on attempt {attempt+1}/{max_retries}")
                # If we get a 401 Unauthorized, we might need to refresh the token
                if hasattr(e, 'response') and e.response.status_code == 401 and attempt < max_retries - 1:
                    log_to_action_log("Attempting to refresh OAuth session...")
                    # Find oauth_session in args or kwargs
                    send_ha_notification("Please reauthorize E*TRADE API")
                    for i, arg in enumerate(args):
                        if hasattr(arg, 'get') and callable(getattr(arg, 'get')):
                            args = list(args)
                            args[i] = get_oauth_session()
                            args = tuple(args)
                            break
                    if 'oauth_session' in kwargs:
                        kwargs['oauth_session'] = get_oauth_session()
            except Exception as e:
                log_to_action_log(f"{func.__name__}: Unexpected error on attempt {attempt+1}/{max_retries}: {str(e)}")
            
            # If we get here, there was an error - wait before retrying
            if attempt < max_retries - 1:
                retry_seconds = retry_delay * (2 ** attempt)  # Exponential backoff
                log_to_action_log(f"Retrying in {retry_seconds} seconds...")
                time.sleep(retry_seconds)
        
        # If we get here, all retries failed
        log_to_action_log(f"{func.__name__}: Failed after {max_retries} attempts")
        
        return None
    
    return wrapper

def save_request_tokens(request_token, request_token_secret, filename="oauth_tokens.json"):
    """Save request token and secret to a file"""
    token_data = {
        "request_token": request_token,
        "request_token_secret": request_token_secret
    }
    with open(filename, 'w') as f:
        json.dump(token_data, f)
    print(f"Request tokens saved to {filename}")

def load_request_tokens(filename="oauth_tokens.json"):
    """Load request token and secret from a file"""
    if not os.path.exists(filename):
        return None, None

    try:
        with open(filename, 'r') as f:
            token_data = json.load(f)
        return token_data.get("request_token"), token_data.get("request_token_secret")
    except (json.JSONDecodeError, KeyError) as e:
        print(f"Error loading tokens from {filename}: {e}")
        return None, None

def cleanup_request_tokens(filename="oauth_tokens.json"):
    """Remove the saved request tokens file after successful authentication"""
    if os.path.exists(filename):
        os.remove(filename)
        print(f"Cleaned up saved tokens file: {filename}")

def get_oauth_session(verification_code=None, oauth_mode=False, preserve_tokens=False):
    """Allows user authorization for the sample application with OAuth 1"""
    etrade = OAuth1Service(
        name="etrade",
        consumer_key=consumer_key,
        consumer_secret=consumer_secret,
        request_token_url="https://api.etrade.com/oauth/request_token",
        access_token_url="https://api.etrade.com/oauth/access_token",
        authorize_url="https://us.etrade.com/e/t/etws/authorize?key={}&token={}",
        base_url="https://api.etrade.com")

    if oauth_mode and verification_code is not None:
        # OAuth mode with verification code: Load saved request tokens
        request_token, request_token_secret = load_request_tokens()
        if request_token is None or request_token_secret is None:
            print("Error: No saved request tokens found. Please run the app with --oauth first.")
            sys.exit(1)

        # Use the provided verification code
        text_code = verification_code
        print(f"Using verification code: {text_code}")
    elif oauth_mode and verification_code is None:
        # OAuth mode without verification code: Get authorization URL and exit
        request_token, request_token_secret = etrade.get_request_token(
            params={"oauth_callback": "oob", "format": "json"})

        # Save the request tokens for later use
        save_request_tokens(request_token, request_token_secret)

        # Display authorization URL and exit
        authorize_url = etrade.authorize_url.format(etrade.consumer_key, request_token)
        print("Authorization URL:")
        print(authorize_url)
        webbrowser.open(authorize_url)
        print("\nPlease visit the URL above, accept the agreement, and get the verification code.")
        print("Then run this app again with --oauth --code YOUR_VERIFICATION_CODE")
        sys.exit(0)
    else:
        # Traditional interactive mode
        request_token, request_token_secret = etrade.get_request_token(
            params={"oauth_callback": "oob", "format": "json"})

        # Go through the authentication flow interactively
        authorize_url = etrade.authorize_url.format(etrade.consumer_key, request_token)
        print(authorize_url)
        webbrowser.open(authorize_url)
        text_code = input("Please accept agreement and enter verification code from browser: ")

    # Step 3: Exchange the authorized request token for an authenticated OAuth 1 session
    try:
        oauth_session = etrade.get_auth_session(request_token,
                                               request_token_secret,
                                               params={"oauth_verifier": text_code})

        # Clean up saved tokens after successful authentication
        if oauth_mode and verification_code is not None:
            if not preserve_tokens:
                cleanup_request_tokens()
            print("OAuth session established successfully!")

        return oauth_session
    except Exception as e:
        if oauth_mode and verification_code is not None:
            print(f"Error: Failed to authenticate with verification code '{verification_code}'")
            print(f"Error during OAuth authentication: {e}")
            print("Please make sure you have the correct verification code from E*TRADE.")
            print("You may need to start the authorization process again by running the app with --oauth")
        else:
            print(f"Error during OAuth authentication: {e}")
        sys.exit(1)

@etrade_api_call
def get_stock_price(symbol, oauth_session):
    if not use_etrade_api:
        return None

    stock_data = None

    global base_url
    url = f"{base_url}/v1/market/quote/{symbol}.json"

    response = oauth_session.get(url, timeout=30)

    if response.status_code == 200:
        data = response.json()
        if data is not None and "QuoteResponse" in data and "QuoteData" in data["QuoteResponse"]:
            for quote in data["QuoteResponse"]["QuoteData"]:
                if quote is not None and "All" in quote:
                    stock_data = StockData(
                        time_record=quote.get('dateTime'),
                        open_price=quote['All'].get('open'),
                        high=quote['All'].get('high'),
                        low=quote['All'].get('low'),
                        last_trade=quote['All'].get('lastTrade'),
                        volume=quote['All'].get('totalVolume'),
                        ask=quote['All'].get('ask'),
                        ask_size=quote['All'].get('askSize'),
                        bid=quote['All'].get('bid'),
                        bid_size=quote['All'].get('bidSize')
                    )
                    # after hours price is All.ExtendedHourQuoteDetail.lastPrice

        else:
            if data is not None and 'QuoteResponse' in data and 'Messages' in data["QuoteResponse"] \
                    and 'Message' in data["QuoteResponse"]["Messages"] \
                    and data["QuoteResponse"]["Messages"]["Message"] is not None:
                for error_message in data["QuoteResponse"]["Messages"]["Message"]:
                    log_to_action_log("Error: " + error_message["description"])
            else:
                log_to_action_log("Error: Quote API service error")
        return stock_data
    else:
        log_to_action_log(f"Error: {response.status_code} - {response.text}")
        return None

@etrade_api_call
def get_account_id(oauth_session):
    order_url = f"{base_url}/v1/accounts/list.json"
    response = oauth_session.get(order_url)
    if response.status_code == 200:
        accounts = response.json().get('AccountListResponse', {}).get('Accounts', {}).get('Account', [])
        if not accounts:
            print("No E*TRADE accounts found.")
            log_to_action_log("No E*TRADE accounts found.")
            return None
        return accounts[0]['accountIdKey']
    else:
        print(f"Failed to get account list: {response.status_code} - {response.text}")
        log_to_action_log(f"Failed to get account list: {response.status_code} - {response.text}")
        return None

# Add function to check order status
@etrade_api_call
def check_order_status(order_id, account_id_key, symbol):
    if not use_etrade_api:
        return None

    if use_sandbox:
        # Randomly set order_id to either "475" (executed) or "477" (open) for SANDBOX testing
        order_id = "475" if random.random() < 0.8 else "477"
        
    # Use list orders API with JSON format instead of XML
    list_orders_url = f"{base_url}/v1/accounts/{account_id_key}/orders.json"
    
    # Add query parameters to filter orders
    params = {
        'count': 25,  # Limit to 25 most recent orders
        'symbol': symbol  # Filter by the symbol we're trading
    }
    
    response = oauth_session.get(list_orders_url, params=params)
    if response.status_code == 200:
        data = response.json()
        
        found_order = False
        status = None
        executed_price = None
        executed_quantity = None
        
        # Navigate through the JSON structure to find orders
        if 'OrdersResponse' in data and 'Order' in data['OrdersResponse']:
            orders = data['OrdersResponse']['Order']
            if not isinstance(orders, list):
                orders = [orders]  # Handle case where there's only one order
                
            # Find our specific order
            for order in orders:
                if str(order.get('orderId')) == str(order_id):
                    found_order = True
                    if 'OrderDetail' in order:
                        if isinstance(order['OrderDetail'], list):
                            orderDetail = order['OrderDetail'][0]
                        else:
                            orderDetail = order['OrderDetail']
                    else:
                        orderDetail = {}
                    status = orderDetail.get('status', '')
                    
                    # Extract execution details if available
                    if 'Instrument' in orderDetail and isinstance(orderDetail['Instrument'], list):
                        instrument = orderDetail['Instrument'][0]
                    else:
                        instrument = orderDetail.get('Instrument', {})
                        
                    executed_price = instrument.get('averageExecutionPrice')
                    executed_quantity = instrument.get('filledQuantity')
                    break
        
        if not found_order:
            log_to_action_log(f"Order {order_id} not found in orders list")
            return {"executed": False, "price": None, "quantity": None, "status": "NOT_FOUND"}
            
        if status == "EXECUTED":
            #print(f"Order {order_id} executed: {executed_quantity} shares at ${executed_price}")
            return {
                "executed": True,
                "price": float(executed_price) if executed_price else None,
                "quantity": int(executed_quantity) if executed_quantity else None,
                "status": status
            }
        else:
            log_to_action_log(f"Order {order_id} status: {status}")
            return {"executed": False, "price": None, "quantity": None, "status": status}
    else:
        log_to_action_log(f"Failed to get orders list: {response.status_code} - {response.text}")
        return {"executed": False, "price": None, "quantity": None, "status": "ERROR"}


@etrade_api_call
def cancel_order(order_id, account_id_key, symbol):
    if not use_etrade_api:
        return True
    
    #print(f"Cancelling order {order_id}...")
    cancel_url = f"{base_url}/v1/accounts/{account_id_key}/orders/cancel.json"
    headers = {'Content-Type': 'application/json'}
    cancel_payload = {
        "CancelOrderRequest": {
            "orderId": order_id
        }
    }
    cancel_resp = oauth_session.put(cancel_url, json=cancel_payload, headers=headers)
    if cancel_resp.status_code == 200:
        time.sleep(1) # wait one second for order to be cancelled
        order_status = check_order_status(order_id, account_id_key, symbol)

        if order_status is not None and order_status["status"] == "CANCELLED":
            log_to_action_log(f"Order {order_id} cancelled successfully")
        else:
            log_to_action_log(f"Failed to cancel order: {order_status}; order status: {order_status['status']}")
        return order_status
    else:
        order_status = check_order_status(order_id, account_id_key, symbol)

        log_to_action_log(f"Failed to cancel order: {cancel_resp.status_code} - {cancel_resp.text}")
        return order_status

def calculate_limit_price(stock_data, order_action, limit_offset_percent=0.5):
    """
    Calculate limit price for limit orders

    Args:
        stock_data: StockData object with current market data
        order_action: "BUY" or "SELL"
        limit_offset_percent: Percentage offset from current price (default 0.5%)

    Returns:
        limit_price: Calculated limit price
    """
    current_price = stock_data.lastTrade
    offset = current_price * (limit_offset_percent / 100)

    if order_action == "BUY":
        # For buy orders, set limit slightly above current price to ensure execution
        limit_price = current_price + offset
    else:  # SELL
        # For sell orders, set limit slightly below current price to ensure execution
        limit_price = current_price - offset

    return round(limit_price, 2)

@etrade_api_call
def preview_order(account_id_key, symbol, order_action, quantity, stock_data=None):
    """
    Preview an order with E*TRADE API

    Args:
        account_id_key: The account ID key
        symbol: The stock symbol
        order_action: "BUY" or "SELL"
        quantity: Number of shares
        stock_data: StockData object (required for LIMIT orders)

    Returns:
        preview_id: The preview ID if successful, None otherwise
    """
    symbol = str(symbol).upper()
    quantity = int(quantity)
    preview_url = f"{base_url}/v1/accounts/{account_id_key}/orders/preview.xml"
    headers = {'Content-Type': 'application/xml'}

    order_detail = {
        "allOrNone": True,
        "orderAction": order_action,
        "priceType": order_type,
        "marketSession": "REGULAR",
        "orderTerm": "GOOD_FOR_DAY",
        "Instrument": {
            "Product": {
                "symbol": symbol,
                "securityType": "EQ"
            },
            "orderAction": order_action,
            "quantityType": "QUANTITY",
            "quantity": quantity
        }
    }

    # Add limit price if using LIMIT orders
    if order_type == "LIMIT":
        if stock_data is None:
            log_to_action_log(f"Error: stock_data required for LIMIT orders")
            return None
        limit_price = calculate_limit_price(stock_data, order_action)
        order_detail["limitPrice"] = limit_price
        log_to_action_log(f"Using LIMIT order with price ${limit_price} for {order_action} {symbol}")
    
    preview_payload = {
        "orderType": "EQ",
        "clientOrderId": str(int(time.time())),
        "Order": order_detail
    }
    
    preview_xml = dicttoxml.dicttoxml(preview_payload, custom_root='PreviewOrderRequest', attr_type=False)
    preview_resp = oauth_session.post(preview_url, data=preview_xml.decode(), headers=headers, timeout=30)
    
    if preview_resp.status_code != 200:
        preview_resp.raise_for_status()
        return None
    
    preview_tree = ET.fromstring(preview_resp.content)
    preview_id = None
    
    for elem in preview_tree.iter():
        if elem.tag.lower().endswith('previewid'):
            preview_id = elem.text
            break
            
    if not preview_id:
        log_to_action_log(f"Could not extract previewId from XML: {preview_resp.text}")
        return None
        
    return preview_id

@etrade_api_call
def place_order(account_id_key, symbol, order_action, quantity, preview_id, stock_data=None):
    """
    Place an order with E*TRADE API using a preview ID

    Args:
        account_id_key: The account ID key
        symbol: The stock symbol
        order_action: "BUY" or "SELL"
        quantity: Number of shares
        preview_id: The preview ID from preview_order
        stock_data: StockData object (required for LIMIT orders)

    Returns:
        order_id: The order ID if successful, None otherwise
    """
    symbol = str(symbol).upper()
    quantity = int(quantity)
    place_url = f"{base_url}/v1/accounts/{account_id_key}/orders/place.xml"
    headers = {'Content-Type': 'application/xml'}

    order_detail = {
        "allOrNone": True,
        "orderAction": order_action,
        "priceType": order_type,
        "marketSession": "REGULAR",
        "orderTerm": "GOOD_FOR_DAY",
        "Instrument": {
            "Product": {
                "symbol": symbol,
                "securityType": "EQ"
            },
            "orderAction": order_action,
            "quantityType": "QUANTITY",
            "quantity": quantity
        }
    }

    # Add limit price if using LIMIT orders
    if order_type == "LIMIT":
        if stock_data is None:
            log_to_action_log(f"Error: stock_data required for LIMIT orders")
            return None
        limit_price = calculate_limit_price(stock_data, order_action)
        order_detail["limitPrice"] = limit_price
    
    place_payload = {
        "orderType": "EQ",
        "clientOrderId": str(int(time.time())),
        "Order": order_detail,
        "PreviewIds": {"previewId": preview_id}
    }
    
    place_xml = dicttoxml.dicttoxml(place_payload, custom_root='PlaceOrderRequest', attr_type=False)
    place_resp = oauth_session.post(place_url, data=place_xml.decode(), headers=headers, timeout=30)
    
    if place_resp.status_code != 200:
        place_resp.raise_for_status()
        return None
    
    place_tree = ET.fromstring(place_resp.content)
    order_id = None
    
    for elem in place_tree.iter():
        if elem.tag.lower().endswith('orderid'):
            order_id = elem.text
            break
    
    if not order_id:
        log_to_action_log(f"Could not extract orderId from XML: {place_resp.text}")
        return None
        
    return order_id

def buy_shares(symbol, count, price, account_id_key, order_time, stock_data=None):
    """
    Buy shares using the E*TRADE API

    Args:
        symbol: The stock symbol
        count: Number of shares to buy
        price: Current price (for logging)
        account_id_key: The account ID key
        order_time: Timestamp for the order
        stock_data: StockData object (required for LIMIT orders)

    Returns:
        order_id: The order ID if successful, None otherwise
    """
    if not use_etrade_api:
        return 0

    # Preview the order
    preview_id = preview_order(account_id_key, symbol, "BUY", count, stock_data)
    if not preview_id:
        return None

    # Place the order
    order_id = place_order(account_id_key, symbol, "BUY", count, preview_id, stock_data)

    return order_id

def sell_shares(symbol, count, price, account_id_key, order_time, stock_data=None):
    """
    Sell shares using the E*TRADE API

    Args:
        symbol: The stock symbol
        count: Number of shares to sell
        price: Current price (for logging)
        account_id_key: The account ID key
        order_time: Timestamp for the order
        stock_data: StockData object (required for LIMIT orders)

    Returns:
        order_id: The order ID if successful, None otherwise
    """
    if not use_etrade_api:
        return 0

    # Preview the order
    preview_id = preview_order(account_id_key, symbol, "SELL", count, stock_data)
    if not preview_id:
        return None

    # Place the order
    order_id = place_order(account_id_key, symbol, "SELL", count, preview_id, stock_data)

    return order_id



def check_and_handle_order(conn, order_id, account_id_key, action, quantity, stock_data, symbol, order_time):
    """
    Check the status of an order and handle it accordingly
    
    Args:
        conn: The database connection
        order_id: The order ID
        account_id_key: The account ID key
        action: The action performed ("buy", "sell", "sellavg")
        quantity: The quantity of shares
        stock_data: The stock price data at the time the order was placed
        symbol: The stock symbol
        order_time: The timestamp when the order was placed
    Returns:
        True if the order was properly handled, False if the order is still open and needs to be rechecked later
    """
    result = check_order_status(order_id, account_id_key, symbol)
    if result is None:
        if use_sandbox:
            result = {}
            result['executed'] = True
            result['price'] = stock_data.lastTrade
            result['quantity'] = quantity
            result['status'] = "EXECUTED"
        else:
            return True
        
    executed = True

    if not result["executed"]:
        executed = False
        cancel_resp = cancel_order(order_id, account_id_key, symbol)

        # double check response from cancel_order; it might have failed or the order might have already been executed
        if cancel_resp is not None and cancel_resp["status"] == "CANCELLED" and not cancel_resp["executed"]:
            result["quantity"] = quantity
            result["price"] = stock_data.lastTrade
            log_action(conn, "cancel_" + action, result["price"], result["quantity"], order_id, order_time, symbol, stock_data)
        elif cancel_resp is not None and cancel_resp["executed"]:
            executed = True
        else:
            return False

    if executed:
        if use_sandbox:
            result["quantity"] = quantity
            result["price"] = round(stock_data.lastTrade, 2)
        result_message = log_action(conn, action, result["price"], result["quantity"], order_id, order_time, symbol, stock_data)
        
        if result_message:
            # Log the full message
            log_to_action_log(result_message["message"])

            # --- Send Home Assistant notification ---
            if action == "buy":
                msg = result_message["short_message"] + "\n" + get_inventory_summary(conn, symbol)["short_message"]
                if not use_sandbox:
                    send_ha_notification(msg)
            elif action == "sell" or action == "sellavg":
                msg = result_message["short_message"] + "\n" + get_profit_summary(conn, symbol)["short_message"]
                if not use_sandbox:
                    send_ha_notification(msg)
    return True
    
def print_price_status(prices_dict):
    # Calculate the width of the longest symbol for alignment
    symbol_width = max(len(sym) for sym in prices_dict.keys())
    # Calculate price_width based on the integer part of the highest price
    max_price = max(abs(stock_data.lastTrade) for stock_data, _ in prices_dict.values())
    int_digits = len(str(int(max_price))) if max_price >= 10 else 2
    wide_price_width = int_digits + 5  # 1 for dot, 4 for decimals
    
    printed_last_updated = False
    for sym, (stock_data, last_checked) in prices_dict.items():
        if not printed_last_updated:
            #print(f"{'='*25} PRICE STATUS - Last Checked: {last_checked} {'='*25}")
            print(f"{'='*15} PRICE STATUS {'='*15}")
            print(f"Last Checked: {last_checked}")
            printed_last_updated = True
        print(f"{sym:<{symbol_width}}: ${stock_data.lastTrade:>{wide_price_width}.4f}  (bid: ${stock_data.bid}, ask: ${stock_data.ask})")
    print("=" * 44 + "\n")

def print_app_status():
    print("E*TRADE API App")
    print("Press Ctrl+C to stop")
    print()
    if use_sandbox:
        print("Using SANDBOX environment")
    else:
        print("Using PRODUCTION environment")
    print(f"Order type: {order_type}")
    print()

def update_display(conn, symbols, prices_dict):
    import os
    

    inventories = get_full_inventory_summary(conn, symbols)
    profits = get_full_profit_summary(conn, symbols)

    os.system('clear')  # Clear the terminal for a dashboard effect
    print_app_status()
    
    print_price_status(prices_dict)
    print("=" * 32 + " INVENTORY " + "=" * 32)
    for symbol, inventory in inventories.items():
        print(inventory['message'])
    print("=" * 75 + "\n")
    
    print("=" * 26 + " PROFITS " + "=" * 26)
    for symbol, profit_msg in profits.items():
        print(profit_msg)
    print("=" * 61 + "\n")
    print()
    print("See action_log.log for rolling log of actions and profits.\n")
    print("Windows: stream_action_log.ps1")
    print("Linux/Mac: tail -f action_log.log")

# --- Home Assistant notification support ---
def send_ha_notification(message):
    config = configparser.ConfigParser()
    config.read('config.ini')
    if not config.has_section('HOME_ASSISTANT'):
        return
    ha_cfg = config['HOME_ASSISTANT']
    enabled = ha_cfg.get('enabled', 'false').strip().lower() == 'true'
    if not enabled:
        return
    ha_url = ha_cfg.get('url', '').rstrip('/')
    ha_token = ha_cfg.get('token', '')
    ha_service = ha_cfg.get('service', 'notify.dan')
    if not ha_url or not ha_token or not ha_service:
        log_to_action_log('HA notification config incomplete')
        return
    endpoint = f"{ha_url}/api/services/{ha_service.replace('.', '/')}"
    headers = {
        "Authorization": f"Bearer {ha_token}",
        "Content-Type": "application/json",
    }
    payload = {"message": message}
    try:
        resp = requests.post(endpoint, headers=headers, json=payload, timeout=5)
        if resp.status_code != 200:
            log_to_action_log(f"Failed to send HA notification: {resp.status_code} - {resp.text}")
    except Exception as e:
        log_to_action_log(f"Exception sending HA notification: {e}")

if __name__ == "__main__":
    def market_is_open():
        now = datetime.now().time()
        market_open = dtime(9, 30)
        market_close = dtime(16, 0)
        return market_open <= now <= market_close

    # Parse command line arguments
    args = parse_arguments()

    # Determine environment settings
    use_sandbox, use_etrade_api = determine_environment(args)

    # Set global variables for other modules
    import builtins
    builtins.use_sandbox = use_sandbox

    # Set API endpoints and credentials based on environment
    if use_sandbox:
        base_url = config["DEFAULT"]["SANDBOX_BASE_URL"]
        consumer_key = config["DEFAULT"]["SB_CONSUMER_KEY"]
        consumer_secret = config["DEFAULT"]["SB_CONSUMER_SECRET"]
        time_to_sleep_before_checking_order = sb_time_to_sleep_before_checking_order
        time_to_sleep_between_trades = sb_time_to_sleep_between_trades
    else:
        base_url = config["DEFAULT"]["PROD_BASE_URL"]
        consumer_key = config["DEFAULT"]["CONSUMER_KEY"]
        consumer_secret = config["DEFAULT"]["CONSUMER_SECRET"]

    config_symbols = config["SYMBOLS"]["symbols"].split(",")
    symbols = [s.strip() for s in config_symbols if s.strip()]
    market_was_open = None

    print_app_status()
    log_to_action_log()
    log_to_action_log("Starting E*TRADE API App...")
    if use_sandbox:
        log_to_action_log("Using SANDBOX environment")
    else:
        log_to_action_log("Using PRODUCTION environment")
    log_to_action_log(f"Order type: {order_type}")
    log_to_action_log()

    if use_etrade_api:
        time.sleep(2)
        oauth_session = get_oauth_session(
            verification_code=args.code,
            oauth_mode=args.oauth,
            preserve_tokens=args.debug_preserve_tokens
        )
        time.sleep(2)
        account_id_key = get_account_id(oauth_session)
        if not account_id_key:
            print("No account_id_key found. Exiting.")
            exit(1)
    else:
        oauth_session = None
        account_id_key = None

    try:
        prices_dict = {sym: ({}, '') for sym in symbols} # key=symbol, value=(stock_data, last_checked)
        while True:
            # reload_config()
            if not use_sandbox and not market_is_open():
                if market_was_open is not None and market_was_open:
                    print(f"{datetime.now()}: Market is closed. Exiting")
                    log_to_action_log("Market is closed. Exiting")
                    exit(0)
                elif market_was_open is None:
                    print(f"{datetime.now()}: Market is closed. Waiting...")
                    log_to_action_log("Market is closed. Waiting...")
                else:
                    stock_data = get_stock_price('AAPL', oauth_session) # dummy call to get_stock_price to check if API is working
                market_was_open = False

                time.sleep(60)
                continue
            market_was_open = market_is_open()

            started_log = False
            orders = []
            for symbol in symbols:
                stock_data = get_stock_price(symbol, oauth_session)
                if use_sandbox:
                    stock_data = StockData(
                        time_record=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        open_price=round(random.uniform(1, 3), 4),
                        high=round(random.uniform(1, 3), 4),
                        low=round(random.uniform(1, 3), 4),
                        last_trade=round(random.uniform(1, 3), 4),
                        volume=random.randint(1, 1000),
                        ask=round(random.uniform(1, 3), 4),
                        ask_size=random.randint(1, 1000),
                        bid=round(random.uniform(1, 3), 4),
                        bid_size=random.randint(1, 1000)
                    )
                    # TODO: Use db function to get random price

                now = datetime.now()

                if stock_data:
                    insert_stock_data(get_connection(), symbol, stock_data)
                    prices_dict[symbol] = (stock_data, now.strftime('%Y-%m-%d %H:%M:%S'))
                    result = act(get_connection(), stock_data, now, symbol)
                    action = result['action']
                    count = result['count']
                    if action.lower() != 'none':
                        if not started_log:
                            log_to_action_log("time")
                            started_log = True
                        log_to_action_log(f"{symbol} Action: {action.upper()} {count} @ {stock_data.lastTrade} (bid: {stock_data.bid}, ask: {stock_data.ask})")
                    elif log_none_action_to_file:
                        if not started_log:
                            log_to_action_log("time")
                            started_log = True
                        log_to_action_log(f"{symbol} Action: {action.lower()} ----->@ {stock_data.lastTrade} (bid: {stock_data.bid}, ask: {stock_data.ask})")
                    order_id = None
                    if action == 'buy' and count > 0:
                        order_id = buy_shares(symbol, count, stock_data.lastTrade, account_id_key, now, stock_data)
                    elif (action == 'sell' or action == 'sellavg' ) and count > 0:
                        order_id = sell_shares(symbol, count, stock_data.lastTrade, account_id_key, now, stock_data)
                    else:
                        if log_none_action_to_db:
                            log_action(get_connection(), "none", 0, 0, None, now, symbol, stock_data)
                    if order_id is not None:
                        orders.append({'symbol': symbol, 'order_id': order_id, 'action': action, 'count': count, 'stock_data': stock_data, 'order_time': now})

            update_display(get_connection(), symbols, prices_dict)

            done_checking_orders = False
            while not done_checking_orders:
                time.sleep(time_to_sleep_before_checking_order)
                
                if started_log:
                    log_to_action_log("===============    results    ===============")
                
                orders_to_recheck = []
                for order in orders:
                    resp = check_and_handle_order(get_connection(), order['order_id'], account_id_key, order['action'], order['count'], order['stock_data'], order['symbol'], order['order_time'])
                    if not resp:
                        orders_to_recheck.append(order)
                if orders_to_recheck:
                    orders = orders_to_recheck
                    log_to_action_log(f"Need to recheck {len(orders)} orders...")
                else:
                    done_checking_orders = True

            update_display(get_connection(), symbols, prices_dict)

            time.sleep(time_to_sleep_between_trades)
    except KeyboardInterrupt:
        print("Stopped by user.")
    finally:
        close_connection()
