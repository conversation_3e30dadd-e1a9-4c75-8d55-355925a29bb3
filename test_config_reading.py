#!/usr/bin/env python3
"""
Test script to verify the ORDER_TYPE configuration reading logic matches what's in app.py.
"""

import configparser

def test_config_reading_logic():
    """Test the exact same logic used in app.py for reading ORDER_TYPE"""
    
    print("Testing ORDER_TYPE configuration reading logic...")
    
    # Read config exactly like app.py does
    config = configparser.ConfigParser()
    config.read('config.ini')
    
    # Test the logic from app.py
    if config.has_section('SETTINGS'):
        # Order type setting (from app.py lines 140-143)
        order_type = config['SETTINGS'].get('ORDER_TYPE', 'MARKET').upper()
        if order_type not in ['MARKET', 'LIMIT']:
            order_type = 'MARKET'  # Default to MARKET if invalid value
        
        print(f"✅ Successfully read ORDER_TYPE from config: {order_type}")
        
        # Verify it matches what we set in config.ini
        raw_value = config['SETTINGS'].get('ORDER_TYPE', 'MARKET')
        print(f"   Raw value from config.ini: '{raw_value}'")
        print(f"   Processed value: '{order_type}'")
        
        if order_type == 'LIMIT':
            print("✅ Configuration correctly set to LIMIT orders")
            return True
        elif order_type == 'MARKET':
            print("ℹ️  Configuration set to MARKET orders")
            return True
        else:
            print(f"❌ Unexpected order type: {order_type}")
            return False
    else:
        print("❌ SETTINGS section not found in config.ini")
        return False

def test_both_configurations():
    """Test both MARKET and LIMIT configurations"""
    
    print("\nTesting both MARKET and LIMIT configurations...")
    
    # Read current config
    config = configparser.ConfigParser()
    config.read('config.ini')
    
    original_value = config['SETTINGS'].get('ORDER_TYPE', 'MARKET')
    print(f"Current ORDER_TYPE in config.ini: {original_value}")
    
    # Test current value
    order_type = original_value.upper()
    if order_type not in ['MARKET', 'LIMIT']:
        order_type = 'MARKET'
    
    print(f"Processed to: {order_type}")
    
    # Test what would happen with different values
    test_values = ['MARKET', 'LIMIT', 'market', 'limit', 'invalid', '']
    
    print("\nTesting different input values:")
    for test_val in test_values:
        processed = test_val.upper() if test_val else 'MARKET'
        if processed not in ['MARKET', 'LIMIT']:
            processed = 'MARKET'
        print(f"  '{test_val}' -> '{processed}'")
    
    return True

def show_config_file_content():
    """Show the relevant part of the config file"""
    
    print("\nCurrent config.ini [SETTINGS] section:")
    print("-" * 40)
    
    config = configparser.ConfigParser()
    config.read('config.ini')
    
    if config.has_section('SETTINGS'):
        for key, value in config['SETTINGS'].items():
            if 'ORDER' in key.upper() or 'TYPE' in key.upper():
                print(f"{key} = {value}")
    
    print("-" * 40)

if __name__ == "__main__":
    print("Testing ORDER_TYPE configuration reading...")
    print("=" * 50)
    
    show_config_file_content()
    success1 = test_config_reading_logic()
    success2 = test_both_configurations()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("✅ All configuration tests passed!")
        print("\nThe ORDER_TYPE configuration is working correctly.")
        print("You can now:")
        print("  - Set ORDER_TYPE = MARKET for market orders (immediate execution)")
        print("  - Set ORDER_TYPE = LIMIT for limit orders (with calculated limit prices)")
    else:
        print("❌ Some tests failed!")
