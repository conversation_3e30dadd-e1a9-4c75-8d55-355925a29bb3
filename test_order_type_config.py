#!/usr/bin/env python3
"""
Test script to verify the ORDER_TYPE configuration is working correctly.
"""

import configparser
import sys
import os

def test_order_type_config():
    """Test that the ORDER_TYPE configuration is read correctly"""
    
    # Read config
    config = configparser.ConfigParser()
    config.read('config.ini')
    
    if not config.has_section('SETTINGS'):
        print("❌ ERROR: SETTINGS section not found in config.ini")
        return False
    
    # Test default value (MARKET)
    order_type = config['SETTINGS'].get('ORDER_TYPE', 'MARKET').upper()
    if order_type not in ['MARKET', 'LIMIT']:
        order_type = 'MARKET'  # Default to MARKET if invalid value
    
    print(f"✅ ORDER_TYPE configuration found: {order_type}")
    
    # Test with different values
    test_cases = [
        ('MARKET', 'MARKET'),
        ('LIMIT', 'LIMIT'),
        ('market', 'MARKET'),  # Test case insensitive
        ('limit', 'LIMIT'),    # Test case insensitive
        ('INVALID', 'MARKET'), # Test invalid value defaults to MARKET
        ('', 'MARKET'),        # Test empty value defaults to MARKET
    ]
    
    print("\nTesting different ORDER_TYPE values:")
    for test_value, expected in test_cases:
        # Simulate the logic from app.py
        test_order_type = test_value.upper() if test_value else 'MARKET'
        if test_order_type not in ['MARKET', 'LIMIT']:
            test_order_type = 'MARKET'
        
        if test_order_type == expected:
            print(f"✅ '{test_value}' -> '{test_order_type}' (expected: '{expected}')")
        else:
            print(f"❌ '{test_value}' -> '{test_order_type}' (expected: '{expected}')")
            return False
    
    return True

def test_config_modification():
    """Test modifying the config and reading it back"""
    
    # Read current config
    config = configparser.ConfigParser()
    config.read('config.ini')
    
    original_value = config['SETTINGS'].get('ORDER_TYPE', 'MARKET')
    print(f"\nOriginal ORDER_TYPE value: {original_value}")
    
    # Test that we can change it to LIMIT
    config['SETTINGS']['ORDER_TYPE'] = 'LIMIT'
    
    # Write to a test file
    with open('test_config.ini', 'w') as f:
        config.write(f)
    
    # Read it back
    test_config = configparser.ConfigParser()
    test_config.read('test_config.ini')
    
    new_value = test_config['SETTINGS'].get('ORDER_TYPE', 'MARKET')
    
    if new_value == 'LIMIT':
        print("✅ Successfully modified and read back ORDER_TYPE = LIMIT")
        success = True
    else:
        print(f"❌ Failed to modify ORDER_TYPE. Got: {new_value}")
        success = False
    
    # Clean up test file
    if os.path.exists('test_config.ini'):
        os.remove('test_config.ini')
    
    return success

if __name__ == "__main__":
    print("Testing ORDER_TYPE configuration...")
    print("=" * 50)
    
    success1 = test_order_type_config()
    success2 = test_config_modification()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("✅ All tests passed! ORDER_TYPE configuration is working correctly.")
        sys.exit(0)
    else:
        print("❌ Some tests failed!")
        sys.exit(1)
