from decimal import Decimal


class StockData:
    """
    Class representing stock data with properties for various price and volume metrics.
    """
    def __init__(self, time_record=None, open_price=None, high=None, low=None, 
                 last_trade=None, volume=None, ask=None, ask_size=None, 
                 bid=None, bid_size=None):
        self.time_record = time_record
        self.open = Decimal(open_price)
        self.high = Decimal(high)
        self.low = Decimal(low)
        self.lastTrade = Decimal(last_trade)
        self.volume = int(volume)
        self.ask = Decimal(ask)
        self.askSize = int(ask_size)
        self.bid = Decimal(bid)
        self.bidSize = int(bid_size)
