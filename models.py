class StockData:
    """
    Class representing stock data with properties for various price and volume metrics.
    """
    def __init__(self, time_record=None, open_price=None, high=None, low=None, 
                 last_trade=None, volume=None, ask=None, ask_size=None, 
                 bid=None, bid_size=None):
        self.time_record = time_record
        self.open = open_price
        self.high = high
        self.low = low
        self.lastTrade = last_trade
        self.volume = volume
        self.ask = ask
        self.askSize = ask_size
        self.bid = bid
        self.bidSize = bid_size
