
-- MySQL dump 10.13  Distrib 5.7.17, for Win64 (x86_64)
--
-- Host: localhost    Database: sm_test1
-- ------------------------------------------------------
-- Server version	5.7.20

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `activity`
--

DROP TABLE IF EXISTS `activity`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `action` varchar(45) DEFAULT NULL,
  `price` decimal(10,5) DEFAULT NULL,
  `cnt` int(11) DEFAULT NULL,
  `process_date` datetime DEFAULT NULL,
  `et_order_id` bigint(20) DEFAULT NULL,
  `symbol` varchar(10) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;


--
-- Table structure for table `activity_sequence`
--

DROP TABLE IF EXISTS `activity_sequence`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_sequence` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;


--
-- Table structure for table `archive_sold`
--

DROP TABLE IF EXISTS `archive_sold`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `archive_sold` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_id` int(11) DEFAULT NULL,
  `inventory_id` int(11) DEFAULT NULL,
  `process_date` datetime DEFAULT NULL,
  `symbol` varchar(10) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;


--
-- Table structure for table `inventory`
--

DROP TABLE IF EXISTS `inventory`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `inventory` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cnt` int(11) DEFAULT NULL,
  `price` decimal(10,5) DEFAULT NULL,
  `process_date` datetime DEFAULT NULL,
  `activity_id` int(11) DEFAULT NULL,
  `symbol` varchar(10) NOT NULL,
  `sale_price_with_next` decimal(10,5) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;


--
-- Table structure for table `inventory_arch`
--

DROP TABLE IF EXISTS `inventory_arch`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `inventory_arch` (
  `id` int(11) NOT NULL,
  `cnt` int(11) DEFAULT NULL,
  `price` decimal(10,5) DEFAULT NULL,
  `process_date` datetime DEFAULT NULL,
  `arch_date` datetime DEFAULT NULL,
  `activity_id` int(11) DEFAULT NULL,
  `symbol` varchar(10) NOT NULL,
  `sale_price_with_next` decimal(10,5) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;


--
-- Table structure for table `inventory_sequence`
--

DROP TABLE IF EXISTS `inventory_sequence`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `inventory_sequence` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;



--
-- Table structure for table `stock_configuration`
--

DROP TABLE IF EXISTS `stock_configuration`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `stock_configuration` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `symbol` varchar(45) NOT NULL,
  `init_sell_margin` decimal(10,5) DEFAULT NULL,
  `init_buy_margin` decimal(10,5) DEFAULT NULL,
  `sell_margin` decimal(10,5) DEFAULT NULL,
  `buy_margin` decimal(10,5) DEFAULT NULL,
  `price_stop_buying` decimal(10,5) DEFAULT NULL,
  `min_shares_to_buy` int(11) DEFAULT NULL,
  `max_shares_to_buy` int(11) DEFAULT NULL,
  `min_sell_margin` decimal(10,5) DEFAULT NULL,
  `max_sell_margin` decimal(10,5) DEFAULT NULL,
  `min_buy_margin` decimal(10,5) DEFAULT NULL,
  `max_buy_margin` decimal(10,5) DEFAULT NULL,
  `margin_step_change` decimal(10,5) DEFAULT NULL,
  `hold_days` int(11) DEFAULT NULL,
  `hold_gain_percent` decimal(10,5) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `symbol_UNIQUE` (`symbol`)
) ENGINE=InnoDB  DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;





DROP TABLE IF EXISTS `inventory_hold`;

CREATE TABLE `inventory_hold` (
  `id` int(11) NOT NULL,
  `cnt` int(11) DEFAULT NULL,
  `price` decimal(10,5) DEFAULT NULL,
  `process_date` datetime DEFAULT NULL,
  `activity_id` int(11) DEFAULT NULL,
  `symbol` varchar(10) NOT NULL,
  `sale_price` decimal(10,5) DEFAULT NULL,
  `dip_level` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;



DROP TABLE IF EXISTS `price_history`;

CREATE TABLE `price_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `quote_timestamp` varchar(64) NOT NULL,
  `open` decimal(10,4) DEFAULT NULL,
  `high` decimal(10,4) DEFAULT NULL,
  `low` decimal(10,4) DEFAULT NULL,
  `last_trade` decimal(10,4) DEFAULT NULL,
  `ask` decimal(10,4) DEFAULT NULL,
  `ask_size` int(11) DEFAULT NULL,
  `bid` decimal(10,4) DEFAULT NULL,
  `bid_size` int(11) DEFAULT NULL,
  `volume` int(11) DEFAULT NULL,
  `symbol` varchar(45) DEFAULT NULL,
  `inserted_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=latin1;













-- MySQL dump 10.13  Distrib 5.7.17, for Win64 (x86_64)
--
-- Host: localhost    Database: sm_test1
-- ------------------------------------------------------
-- Server version	5.7.20

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Dumping routines for database 'sm_test1'
--
/*!50003 DROP PROCEDURE IF EXISTS `get_action` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `get_action`( in i_market_price decimal(10,5),
								in i_symbol varchar(10),
                                in i_bid_price  decimal(10,5),
                                in i_ask_price  decimal(10,5),
                                in i_bid_size int,
                                in i_ask_size int,
                                out o_action  varchar(10),
                                out o_cnt int
							)
BEGIN

DECLARE l_cnt_can_profit_from INT DEFAULT 0;
DECLARE l_current_lowest_inventory_price decimal(10,5);
DECLARE l_cnt_inventory_total INT;
DECLARE l_cnt_at_lowest_price INT;
declare l_last_action varchar(45);

declare l_sell_margin decimal(10,5);
declare l_buy_margin decimal(10,5);
declare l_price_stop_buying decimal(10,5);
declare l_min_shares_to_buy int;
declare l_max_shares_to_buy int;

DECLARE l_shares_to_buy INT;


SELECT sell_margin,buy_margin,price_stop_buying,min_shares_to_buy,max_shares_to_buy
into l_sell_margin,l_buy_margin,l_price_stop_buying,l_min_shares_to_buy,l_max_shares_to_buy	
FROM  stock_configuration
where symbol=i_symbol;

	-- take count on number of stocks that were bought below  Bid price (bid price is what is availbale from people who want to buy)
    -- if currently sold will result in at least in the sell margin profit combined
	select IFNULL(sum(cnt),0) into l_cnt_can_profit_from
    from inventory
    where symbol =i_symbol 
    and ( price + l_sell_margin < i_bid_price
     or sale_price_with_next + l_sell_margin < i_bid_price
    );

	-- check if any inventory at all and if so get lowest price
	select min(price), ifnull(sum(cnt),0) into l_current_lowest_inventory_price, l_cnt_inventory_total
	from inventory
	where symbol = i_symbol;
    
	-- get current lowest price shares count
	select IFNULL(max(cnt),0) into l_cnt_at_lowest_price
	from inventory
	where price = l_current_lowest_inventory_price
	and symbol = i_symbol;    

	set l_shares_to_buy = least(
								greatest(l_cnt_at_lowest_price+1, l_min_shares_to_buy),
                                        l_max_shares_to_buy);    


	select action into l_last_action
    from activity
    where action not in  ( 'none','cancel-buy', 'cancel-sell', 'cancel-sellavg', 'cancel-buyhold','cancel-sellhold')
    and symbol= i_symbol
    and id = (select max(id) from activity
				where action not in  ( 'none','cancel-buy', 'cancel-sell', 'cancel-sellavg','cancel-buyhold','cancel-sellhold')
				and symbol= i_symbol);



    if l_cnt_can_profit_from = 0 then
		
        
        if  l_cnt_inventory_total = 0 then
			
			-- Ask price is what is available for immediate sale from people on the Market and if below Stopper buy the minimium starting shares
			if i_ask_price < l_price_stop_buying then

                    set o_action = 'buy';
                    set o_cnt = l_min_shares_to_buy;
                    
            else
					set o_action = 'none';
                    set o_cnt = 0;

			end if;
        -- Ask price available for immediate sale from people on the Market gone down below the desired    
        elseif i_ask_price < l_price_stop_buying and (l_current_lowest_inventory_price - i_ask_price > l_buy_margin) then
			set o_action = 'buy';
			set o_cnt = l_shares_to_buy;            
        else
			-- if last action was sell and avg computing was in place consider lower buy margin
			if l_last_action = 'sellavg' and l_current_lowest_inventory_price - i_ask_price > l_buy_margin/2 then
				set o_action = 'buy';
				set o_cnt = l_shares_to_buy; 			
			else
				set o_action = 'none';
				set o_cnt = 0; 			
			end if;
		
		end if;
    
    else -- l_cnt_can_profit_from > 0 
    
		if l_cnt_can_profit_from > l_cnt_at_lowest_price then
			set o_action = 'sellavg';
			set o_cnt = l_cnt_can_profit_from;             
        else    
			set o_action = 'sell';
			set o_cnt = l_cnt_can_profit_from;            
		end if;
    
    end if;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `get_hold_action` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `get_hold_action`( in i_market_price decimal(10,5),
								in i_symbol varchar(10),
                                in i_bid_price  decimal(10,5),
                                in i_ask_price  decimal(10,5),
                                in i_bid_size int,
                                in i_ask_size int,
                                out o_action  varchar(10),
                                out o_cnt int
							)
BEGIN


DECLARE l_current_lowest_inventory_price decimal(10,5);
DECLARE l_cnt_at_lowest_price INT;

declare l_sell_margin decimal(10,5);
declare l_hold_days int;

DECLARE l_shares_to_buy INT;
DECLARE l_cnt_last_dip_level INT;
DECLARE l_dip_level_in_inventory INT;
DECLARE l_last_dip_level_in_hold INT;
DECLARE l_date_bought datetime;
DECLARE l_last_dip_level_sale_price decimal(10,5);

DECLARE l_last_dip_level_purchase_price decimal(10,5);




SELECT sell_margin, hold_days
into l_sell_margin, l_hold_days
FROM  stock_configuration
where symbol=i_symbol;



	-- take info from latest dip_level
	select  ifnull (max(cnt),0), ifnull(max(process_date), now()), ifnull(max(price),0), 
			ifnull (max(sale_price),0), ifnull(max(dip_level),0)
    into l_cnt_last_dip_level, l_date_bought, l_last_dip_level_purchase_price, 
			l_last_dip_level_sale_price, l_last_dip_level_in_hold
    from inventory_hold
    where symbol =i_symbol 
    and id = (select max(id) from inventory_hold where symbol =i_symbol) ;

	-- check if any inventory at all and if so get lowest price and dip level
	select min(price) , count(1)
    into l_current_lowest_inventory_price, l_dip_level_in_inventory
	from inventory
	where symbol = i_symbol;
    
	-- get current lowest price shares count
	select IFNULL(max(cnt),0) into l_cnt_at_lowest_price
	from inventory
	where price = l_current_lowest_inventory_price
	and symbol = i_symbol;    
	
    -- if price keep folling every 4 consequetive falls if no hold for it , buy to hold
	if mod (l_dip_level_in_inventory , 4) = 0 and l_dip_level_in_inventory > l_last_dip_level_in_hold then    
    	set l_shares_to_buy = l_cnt_at_lowest_price;
    else 
		set l_shares_to_buy = 0;
    end if;


    -- if price keep going down slow a bit buying by increaing the buy margin
	if mod (l_dip_level_in_inventory , 7) = 0 then    

		update stock_configuration
		set buy_margin = init_buy_margin + margin_step_change
		where symbol = i_symbol;

    end if;

    if l_shares_to_buy > 0 then
		set o_action = 'buyhold';
		set o_cnt = l_shares_to_buy; 		
    elseif (l_cnt_last_dip_level >0 and l_last_dip_level_sale_price < i_market_price )
			 or (l_cnt_last_dip_level >0  and l_last_dip_level_purchase_price + l_sell_margin < i_market_price 
			 and l_date_bought < DATE_ADD(NOW(), INTERVAL -l_hold_days DAY) ) 
            then
		set o_action = 'sellhold';
		set o_cnt = l_cnt_last_dip_level; 
	else					
		set o_action = 'none';
		set o_cnt = 0;    
    end if;
    
    

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `log_action` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `log_action`(	i_action varchar(45),
								in i_cnt int, 
								in i_price decimal(10,5),
                                in i_date datetime,
                                in i_symbol varchar(10),
                                in i_order_id int,
                                in i_requested_price decimal(10,5),
                                out o_activity_id int
                                )
BEGIN

DECLARE l_sell_margin decimal(10,5);
DECLARE l_margin_step_change decimal(10,5);
DECLARE l_hold_gain_percent decimal(10,5);

SELECT sell_margin, margin_step_change, hold_gain_percent
into l_sell_margin, l_margin_step_change, l_hold_gain_percent
FROM  stock_configuration
where symbol=i_symbol;

	-- generate next id for activity 
	insert into activity_sequence values();
	set o_activity_id = LAST_INSERT_ID();

	if i_action = 'buy' then
		-- insert into activity, inventory and update the avg price record
		call log_buy_action (i_cnt,i_price,i_date,i_symbol,o_activity_id,i_order_id);
                
        update stock_configuration
			set buy_margin = greatest(buy_margin - l_margin_step_change , min_buy_margin),
				sell_margin = least(sell_margin + l_margin_step_change , max_sell_margin)
        where symbol = i_symbol;

    elseif  i_action = 'buyhold' then
    
		call log_buyhold_action (i_cnt, i_price, i_date, i_symbol, l_hold_gain_percent, o_activity_id, i_order_id);    
        
    elseif  i_action in ( 'sell', 'sellavg') then
		call log_sell_action (i_cnt,i_price,i_date,i_symbol,l_sell_margin, i_action, o_activity_id, i_order_id, i_requested_price);

        update stock_configuration
			set sell_margin = greatest(sell_margin - l_margin_step_change , min_sell_margin),
				buy_margin = least(buy_margin + l_margin_step_change , max_buy_margin)
        where symbol = i_symbol;   
        
    elseif i_action = 'sellhold' then
    
		call log_sellhold_action (i_cnt,i_price,i_date,i_symbol,l_sell_margin, i_action, o_activity_id, i_order_id, i_requested_price);
    
    else -- i_action in ( 'cancel-buy', 'cancel-sell', 'cancel-sellavg' ,'cancel-buyhold','cancel-sellhold') then
		insert into activity (id, action,price, cnt,process_date, symbol, et_order_id) 
		values (o_activity_id, i_action, i_price, i_cnt, i_date, i_symbol,i_order_id);
        commit;
    end if;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `log_buyhold_action` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `log_buyhold_action`(	in i_cnt int, 
								in i_price decimal(10,5),
                                in i_date datetime,
                                in i_symbol varchar(10),
                                in i_gain_percent decimal(10,5),
                                in i_activity_id int,
                                in i_order_id int
                                )
BEGIN

declare l_activity_id int;
declare l_inventory_id int;
declare l_dip_level_in_inventory int;  
declare l_initial_inventory_price decimal(10,5);
declare l_current_lowest_inventory_price decimal(10,5);

declare l_sale_price decimal(10,5);

	set l_activity_id = i_activity_id;
    
    -- generate next id for inventory 
	insert into inventory_sequence values();
	set l_inventory_id = LAST_INSERT_ID();

	select min(price), max(price), count(1)
    into l_current_lowest_inventory_price, l_initial_inventory_price, l_dip_level_in_inventory
	from inventory
	where symbol = i_symbol;
 
	set l_sale_price = i_price + i_gain_percent * (l_initial_inventory_price - l_current_lowest_inventory_price);
        
	insert into activity (id, action,price, cnt,process_date, symbol, et_order_id) 
    values (l_activity_id, 'buyhold', i_price, i_cnt, i_date, i_symbol, i_order_id);

    -- insert the new buy record data
	insert into inventory_hold (id, cnt,price, process_date, activity_id,symbol, sale_price, dip_level) 
    values (l_inventory_id,i_cnt, i_price, i_date, l_activity_id, i_symbol,l_sale_price,l_dip_level_in_inventory);
    

    commit;
        
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `log_buy_action` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `log_buy_action`(	in i_cnt int, 
								in i_price decimal(10,5),
                                in i_date datetime,
                                in i_symbol varchar(10),
                                in i_activity_id int,
                                in i_order_id int
                                )
BEGIN

declare l_activity_id int;
declare l_inventory_id int;
declare l_avg_inventory_id int;
declare l_avg_inventory_cnt int;  
declare l_avg_inventory_price decimal(10,5);
declare l_avg_price decimal(10,5);


	set l_activity_id = i_activity_id;
    
    -- generate next id for inventory 
	insert into inventory_sequence values();
	set l_inventory_id = LAST_INSERT_ID();
   
	-- get record if exists to be updated with avg sale price before new buy record is inseted
	select IFNULL(id,0), IFNULL(cnt,0), IFNULL(price,0)
	into l_avg_inventory_id , l_avg_inventory_cnt, l_avg_inventory_price
	from inventory 
	where symbol=i_symbol
	and id = (select max(id) from inventory where symbol=i_symbol);
        
	insert into activity (id, action,price, cnt,process_date, symbol, et_order_id) 
    values (l_activity_id, 'buy', i_price, i_cnt, i_date, i_symbol,i_order_id);

    -- insert the new buy record data
	insert into inventory (id, cnt,price, process_date, activity_id,symbol) 
    values (l_inventory_id,i_cnt, i_price, i_date, l_activity_id, i_symbol);

	-- if previous record existed
    if l_avg_inventory_id>0 then
		--  calculate the avg price on last 2 record (newly inserted and previous last)
		set l_avg_price = round( ( (i_cnt*i_price) + 
							(l_avg_inventory_cnt*l_avg_inventory_price) 
						  ) / (i_cnt+l_avg_inventory_cnt) ,4);
        
        -- update the previous record after the new record is inserted with the avg sale price for both
        update inventory
        set sale_price_with_next = l_avg_price
        where id = l_avg_inventory_id;
        

    end if;
    

        
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `log_sellhold_action` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `log_sellhold_action`(in i_cnt int, 
								in i_price decimal(10,5),
                                in i_date datetime,
                                in i_symbol varchar(10),
                                in i_sell_margin  decimal(10,5),
                                in i_sell_type varchar(45),
                                in i_activity_id int,
                                in i_order_id int,
                                in i_requested_price decimal(10,5)
                                )
BEGIN

declare l_sell_margin decimal(10,5);
declare l_activity_id int;
declare l_last_inventory_id int;

declare l_last_hold_id int;

	set l_activity_id = i_activity_id;
	set l_sell_margin = i_sell_margin;

	insert into activity (id, action, price, cnt, process_date, symbol, et_order_id) 
	values (l_activity_id, i_sell_type, i_price, i_cnt, i_date, i_symbol,i_order_id);
	
	
	insert into archive_sold (activity_id, inventory_id, process_date, symbol)
	select l_activity_id, id, i_date, symbol
	from inventory_hold
	where symbol = i_symbol
    and id = (select max(id) from inventory_hold where symbol = i_symbol );
	
	insert into inventory_arch (id,cnt,price, process_date,arch_date,activity_id, symbol, sale_price_with_next)
	select id,cnt,price, process_date,i_date, activity_id,symbol,sale_price
	from inventory_hold
	where symbol = i_symbol
    and id = (select max(id) from inventory_hold where symbol = i_symbol );



    select max(id) into l_last_hold_id
    from inventory_hold where symbol = i_symbol;
	
	delete from inventory_hold
	where symbol = i_symbol
    and id = l_last_hold_id;
    
    commit; 
    
    
        
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `log_sell_action` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `log_sell_action`(in i_cnt int, 
								in i_price decimal(10,5),
                                in i_date datetime,
                                in i_symbol varchar(10),
                                in i_sell_margin  decimal(10,5),
                                in i_sell_type varchar(45),
                                in i_activity_id int,
                                in i_order_id int,
                                in i_requested_price decimal(10,5)
                                )
BEGIN

declare l_sell_margin decimal(10,5);
declare l_activity_id int;
declare l_last_inventory_id int;

	set l_activity_id = i_activity_id;
	set l_sell_margin = i_sell_margin;

	insert into activity (id, action, price, cnt, process_date, symbol, et_order_id) 
	values (l_activity_id, i_sell_type, i_price, i_cnt, i_date, i_symbol,i_order_id);
	
	
	insert into archive_sold (activity_id, inventory_id, process_date, symbol)
	select l_activity_id, id, i_date, symbol
	from inventory
	where symbol = i_symbol
    and id>0
    and (price + l_sell_margin < i_requested_price or sale_price_with_next + l_sell_margin < i_requested_price);
	
	insert into inventory_arch (id,cnt,price, process_date,arch_date,activity_id, symbol, sale_price_with_next)
	select id,cnt,price, process_date,i_date, activity_id,symbol,sale_price_with_next
	from inventory
	where id>0 
    and symbol = i_symbol
	and (price + l_sell_margin < i_requested_price or sale_price_with_next + l_sell_margin < i_requested_price);
	
	delete from inventory
	where id>0 
	and symbol = i_symbol
	and (price + l_sell_margin < i_requested_price or sale_price_with_next + l_sell_margin < i_requested_price);
    
    commit; 
 
 
    select max(id) into l_last_inventory_id
    from inventory where symbol = i_symbol;
    
    update inventory
		set sale_price_with_next = null
    where symbol = i_symbol
    and id = l_last_inventory_id;
    
    
    commit;
    
        
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-05 14:18:17

