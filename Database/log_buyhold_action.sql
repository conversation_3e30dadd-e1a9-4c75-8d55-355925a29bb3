CREATE DEFINER=`root`@`localhost` PROCEDURE `log_buyhold_action`(	in i_cnt int, 
								in i_price decimal(10,5),
                                in i_date datetime,
                                in i_symbol varchar(10),
                                in i_gain_percent decimal(10,5),
                                in i_activity_id int,
                                in i_order_id int
                                )
BEGIN

declare l_activity_id int;
declare l_inventory_id int;
declare l_dip_level_in_inventory int;  
declare l_initial_inventory_price decimal(10,5);
declare l_current_lowest_inventory_price decimal(10,5);

declare l_sale_price decimal(10,5);

	set l_activity_id = i_activity_id;
    
    -- generate next id for inventory 
	insert into inventory_sequence values();
	set l_inventory_id = LAST_INSERT_ID();

	select min(price), max(price), count(1)
    into l_current_lowest_inventory_price, l_initial_inventory_price, l_dip_level_in_inventory
	from inventory
	where symbol = i_symbol;
 
	set l_sale_price = i_price + i_gain_percent * (l_initial_inventory_price - l_current_lowest_inventory_price);
        
	insert into activity (id, action,price, cnt,process_date, symbol, et_order_id) 
    values (l_activity_id, 'buyhold', i_price, i_cnt, i_date, i_symbol, i_order_id);

    -- insert the new buy record data
	insert into inventory_hold (id, cnt,price, process_date, activity_id,symbol, sale_price, dip_level) 
    values (l_inventory_id,i_cnt, i_price, i_date, l_activity_id, i_symbol,l_sale_price,l_dip_level_in_inventory);
    

    commit;
        
END