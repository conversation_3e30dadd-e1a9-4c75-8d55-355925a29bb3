CREATE DEFINER=`root`@`localhost` PROCEDURE `log_sellhold_action`(in i_cnt int, 
								in i_price decimal(10,5),
                                in i_date datetime,
                                in i_symbol varchar(10),
                                in i_sell_margin  decimal(10,5),
                                in i_sell_type varchar(45),
                                in i_activity_id int,
                                in i_order_id int,
                                in i_requested_price decimal(10,5)
                                )
BEGIN

declare l_sell_margin decimal(10,5);
declare l_activity_id int;
declare l_last_inventory_id int;

declare l_last_hold_id int;

	set l_activity_id = i_activity_id;
	set l_sell_margin = i_sell_margin;

	insert into activity (id, action, price, cnt, process_date, symbol, et_order_id) 
	values (l_activity_id, i_sell_type, i_price, i_cnt, i_date, i_symbol,i_order_id);
	
	
	insert into archive_sold (activity_id, inventory_id, process_date, symbol)
	select l_activity_id, id, i_date, symbol
	from inventory_hold
	where symbol = i_symbol
    and id = (select max(id) from inventory_hold where symbol = i_symbol );
	
	insert into inventory_arch (id,cnt,price, process_date,arch_date,activity_id, symbol, sale_price_with_next)
	select id,cnt,price, process_date,i_date, activity_id,symbol,sale_price
	from inventory_hold
	where symbol = i_symbol
    and id = (select max(id) from inventory_hold where symbol = i_symbol );



    select max(id) into l_last_hold_id
    from inventory_hold where symbol = i_symbol;
	
	delete from inventory_hold
	where symbol = i_symbol
    and id = l_last_hold_id;
    
    commit; 
    
    
        
END