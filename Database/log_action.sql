CREATE DEFINER=`root`@`localhost` PROCEDURE `log_action`(	i_action varchar(45),
								in i_cnt int, 
								in i_price decimal(10,5),
                                in i_date datetime,
                                in i_symbol varchar(10),
                                in i_order_id int,
                                in i_requested_price decimal(10,5),
                                out o_activity_id int
                                )
BEGIN

DECLARE l_sell_margin decimal(10,5);
DECLARE l_margin_step_change decimal(10,5);
DECLARE l_hold_gain_percent decimal(10,5);

SELECT sell_margin, margin_step_change, hold_gain_percent
into l_sell_margin, l_margin_step_change, l_hold_gain_percent
FROM  stock_configuration
where symbol=i_symbol;

	-- generate next id for activity 
	insert into activity_sequence values();
	set o_activity_id = LAST_INSERT_ID();

	if i_action = 'buy' then
		-- insert into activity, inventory and update the avg price record
		call log_buy_action (i_cnt,i_price,i_date,i_symbol,o_activity_id,i_order_id);
                
        update stock_configuration
			set buy_margin = greatest(buy_margin - l_margin_step_change , min_buy_margin),
				sell_margin = least(sell_margin + l_margin_step_change , max_sell_margin)
        where symbol = i_symbol;

    elseif  i_action = 'buyhold' then
    
		call log_buyhold_action (i_cnt, i_price, i_date, i_symbol, l_hold_gain_percent, o_activity_id, i_order_id);    
        
    elseif  i_action in ( 'sell', 'sellavg') then
		call log_sell_action (i_cnt,i_price,i_date,i_symbol,l_sell_margin, i_action, o_activity_id, i_order_id, i_requested_price);

        update stock_configuration
			set sell_margin = greatest(sell_margin - l_margin_step_change , min_sell_margin),
				buy_margin = least(buy_margin + l_margin_step_change , max_buy_margin)
        where symbol = i_symbol;   
        
    elseif i_action = 'sellhold' then
    
		call log_sellhold_action (i_cnt,i_price,i_date,i_symbol,l_sell_margin, i_action, o_activity_id, i_order_id, i_requested_price);
    
    else -- i_action in ( 'cancel-buy', 'cancel-sell', 'cancel-sellavg' ,'cancel-buyhold','cancel-sellhold') then
		insert into activity (id, action,price, cnt,process_date, symbol, et_order_id) 
		values (o_activity_id, i_action, i_price, i_cnt, i_date, i_symbol,i_order_id);
        commit;
    end if;

END