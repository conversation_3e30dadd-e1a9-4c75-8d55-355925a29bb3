CREATE DEFINER=`root`@`localhost` PROCEDURE `get_hold_action`( in i_market_price decimal(10,5),
								in i_symbol varchar(10),
                                in i_bid_price  decimal(10,5),
                                in i_ask_price  decimal(10,5),
                                in i_bid_size int,
                                in i_ask_size int,
                                out o_action  varchar(10),
                                out o_cnt int
							)
BEGIN


DECLARE l_current_lowest_inventory_price decimal(10,5);
DECLARE l_cnt_at_lowest_price INT;

declare l_sell_margin decimal(10,5);
declare l_hold_days int;

DECLARE l_shares_to_buy INT;
DECLARE l_cnt_last_dip_level INT;
DECLARE l_dip_level_in_inventory INT;
DECLARE l_last_dip_level_in_hold INT;
DECLARE l_date_bought datetime;
DECLARE l_last_dip_level_sale_price decimal(10,5);

DECLARE l_last_dip_level_purchase_price decimal(10,5);




SELECT sell_margin, hold_days
into l_sell_margin, l_hold_days
FROM  stock_configuration
where symbol=i_symbol;



	-- take info from latest dip_level
	select  ifnull (max(cnt),0), ifnull(max(process_date), now()), ifnull(max(price),0), 
			ifnull (max(sale_price),0), ifnull(max(dip_level),0)
    into l_cnt_last_dip_level, l_date_bought, l_last_dip_level_purchase_price, 
			l_last_dip_level_sale_price, l_last_dip_level_in_hold
    from inventory_hold
    where symbol =i_symbol 
    and id = (select max(id) from inventory_hold where symbol =i_symbol) ;

	-- check if any inventory at all and if so get lowest price and dip level
	select min(price) , count(1)
    into l_current_lowest_inventory_price, l_dip_level_in_inventory
	from inventory
	where symbol = i_symbol;
    
	-- get current lowest price shares count
	select IFNULL(max(cnt),0) into l_cnt_at_lowest_price
	from inventory
	where price = l_current_lowest_inventory_price
	and symbol = i_symbol;    
	
    -- if price keep folling every 4 consequetive falls if no hold for it , buy to hold
	if mod (l_dip_level_in_inventory , 4) = 0 and l_dip_level_in_inventory > l_last_dip_level_in_hold then    
    	set l_shares_to_buy = l_cnt_at_lowest_price;
    else 
		set l_shares_to_buy = 0;
    end if;


    -- if price keep going down slow a bit buying by increaing the buy margin
	if mod (l_dip_level_in_inventory , 7) = 0 then    

		update stock_configuration
		set buy_margin = init_buy_margin + margin_step_change
		where symbol = i_symbol;

    end if;

    if l_shares_to_buy > 0 then
		set o_action = 'buyhold';
		set o_cnt = l_shares_to_buy; 		
    elseif (l_cnt_last_dip_level >0 and l_last_dip_level_sale_price < i_market_price )
			 or (l_cnt_last_dip_level >0  and l_last_dip_level_purchase_price + l_sell_margin < i_market_price 
			 and l_date_bought < DATE_ADD(NOW(), INTERVAL -l_hold_days DAY) ) 
            then
		set o_action = 'sellhold';
		set o_cnt = l_cnt_last_dip_level; 
	else					
		set o_action = 'none';
		set o_cnt = 0;    
    end if;
    
    

END