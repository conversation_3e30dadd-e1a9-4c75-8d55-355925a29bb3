CREATE DEFINER=`root`@`localhost` PROCEDURE `get_action`( in i_market_price decimal(10,5),
								in i_symbol varchar(10),
                                in i_bid_price  decimal(10,5),
                                in i_ask_price  decimal(10,5),
                                in i_bid_size int,
                                in i_ask_size int,
                                out o_action  varchar(10),
                                out o_cnt int
							)
BEGIN

DECLARE l_cnt_can_profit_from INT DEFAULT 0;
DECLARE l_current_lowest_inventory_price decimal(10,5);
DECLARE l_cnt_inventory_total INT;
DECLARE l_cnt_at_lowest_price INT;
declare l_last_action varchar(45);

declare l_sell_margin decimal(10,5);
declare l_buy_margin decimal(10,5);
declare l_price_stop_buying decimal(10,5);
declare l_min_shares_to_buy int;
declare l_max_shares_to_buy int;

DECLARE l_shares_to_buy INT;


SELECT sell_margin,buy_margin,price_stop_buying,min_shares_to_buy,max_shares_to_buy
into l_sell_margin,l_buy_margin,l_price_stop_buying,l_min_shares_to_buy,l_max_shares_to_buy	
FROM  stock_configuration
where symbol=i_symbol;

	-- take count on number of stocks that were bought below  Bid price (bid price is what is availbale from people who want to buy)
    -- if currently sold will result in at least in the sell margin profit combined
	select IFNULL(sum(cnt),0) into l_cnt_can_profit_from
    from inventory
    where symbol =i_symbol 
    and ( price + l_sell_margin < i_bid_price
     or sale_price_with_next + l_sell_margin < i_bid_price
    );

	-- check if any inventory at all and if so get lowest price
	select min(price), ifnull(sum(cnt),0) into l_current_lowest_inventory_price, l_cnt_inventory_total
	from inventory
	where symbol = i_symbol;
    
	-- get current lowest price shares count
	select IFNULL(max(cnt),0) into l_cnt_at_lowest_price
	from inventory
	where price = l_current_lowest_inventory_price
	and symbol = i_symbol;    

	set l_shares_to_buy = least(
								greatest(l_cnt_at_lowest_price+1, l_min_shares_to_buy),
                                        l_max_shares_to_buy);    


	select action into l_last_action
    from activity
    where action <> 'none' 
    and action not like  'cancel%' 
    and symbol= i_symbol
    and id = (select max(id) from activity
				where action <> 'none' 
                and action not like  'cancel%' 
				and symbol= i_symbol);



    if l_cnt_can_profit_from = 0 then
		
        
        if  l_cnt_inventory_total = 0 then
			
			-- Ask price is what is available for immediate sale from people on the Market and if below Stopper buy the minimium starting shares
			if i_ask_price < l_price_stop_buying then

                    set o_action = 'buy';
                    set o_cnt = l_min_shares_to_buy;
                    
            else
					set o_action = 'none';
                    set o_cnt = 0;

			end if;
        -- Ask price available for immediate sale from people on the Market gone down below the desired    
        elseif i_ask_price < l_price_stop_buying and (l_current_lowest_inventory_price - i_ask_price > l_buy_margin) then
			set o_action = 'buy';
			set o_cnt = l_shares_to_buy;            
        else
			-- if last action was sell and avg computing was in place consider lower buy margin
			if l_last_action = 'sellavg' and l_current_lowest_inventory_price - i_ask_price > l_buy_margin/2 then
				set o_action = 'buy';
				set o_cnt = l_shares_to_buy; 			
			else
				set o_action = 'none';
				set o_cnt = 0; 			
			end if;
		
		end if;
    
    else -- l_cnt_can_profit_from > 0 
    
		if l_cnt_can_profit_from > l_cnt_at_lowest_price then
			set o_action = 'sellavg';
			set o_cnt = l_cnt_can_profit_from;             
        else    
			set o_action = 'sell';
			set o_cnt = l_cnt_can_profit_from;            
		end if;
    
    end if;

END