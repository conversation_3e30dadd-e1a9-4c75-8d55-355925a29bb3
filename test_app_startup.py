#!/usr/bin/env python3
"""
Test script to verify the app correctly reads ORDER_TYPE configuration at startup.
"""

import subprocess
import sys
import time

def test_app_startup_with_limit():
    """Test that the app shows ORDER_TYPE: LIMIT in startup messages"""
    
    print("Testing app startup with ORDER_TYPE = LIMIT...")
    print("This will start the app in 'none' mode (no E*TRADE API) and check startup messages.")
    
    try:
        # Start the app in 'none' mode so it doesn't try to connect to E*TRADE
        # and capture the startup output
        process = subprocess.Popen(
            ['python', 'app.py', '--mode', 'none'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Give it a few seconds to start up and show initial messages
        time.sleep(3)
        
        # Terminate the process
        process.terminate()
        
        # Get the output
        stdout, stderr = process.communicate(timeout=5)
        
        print("Startup output:")
        print("-" * 40)
        print(stdout)
        print("-" * 40)
        
        if stderr:
            print("Errors:")
            print(stderr)
        
        # Check if ORDER_TYPE: LIMIT appears in the output
        if "Order type: LIMIT" in stdout:
            print("✅ SUCCESS: Found 'Order type: LIMIT' in startup output")
            return True
        else:
            print("❌ FAILED: 'Order type: LIMIT' not found in startup output")
            print("Looking for any order type mentions...")
            lines = stdout.split('\n')
            for line in lines:
                if 'order type' in line.lower() or 'Order type' in line:
                    print(f"Found: {line}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Process timed out")
        process.kill()
        return False
    except Exception as e:
        print(f"❌ Error running test: {e}")
        return False

if __name__ == "__main__":
    print("Testing app startup with ORDER_TYPE configuration...")
    print("=" * 60)
    
    success = test_app_startup_with_limit()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ Test passed! App correctly shows ORDER_TYPE configuration.")
        sys.exit(0)
    else:
        print("❌ Test failed!")
        sys.exit(1)
